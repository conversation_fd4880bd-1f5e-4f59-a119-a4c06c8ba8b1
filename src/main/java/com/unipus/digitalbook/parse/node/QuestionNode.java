package com.unipus.digitalbook.parse.node;

import java.util.ArrayList;
import java.util.List;

/**
 * 题目节点对象
 */
public class QuestionNode {
    private String questionType;
    private String questionGroup;
    private String questionData;
    private List<String> videoUrls = new ArrayList<>();

    // Getters and Setters
    public String getQuestionType() { return questionType; }
    public void setQuestionType(String questionType) { this.questionType = questionType; }

    public String getQuestionGroup() { return questionGroup; }
    public void setQuestionGroup(String questionGroup) { this.questionGroup = questionGroup; }

    public String getQuestionData() { return questionData; }
    public void setQuestionData(String questionData) { this.questionData = questionData; }

    public List<String> getVideoUrls() { return videoUrls; }
    public void setVideoUrls(List<String> videoUrls) { this.videoUrls = videoUrls; }

    @Override
    public String toString() {
        return "QuestionNode{" +
                "questionType='" + questionType + '\'' +
                ", questionGroup='" + questionGroup + '\'' +
                ", videoUrls=" + videoUrls +
                '}';
    }
}
