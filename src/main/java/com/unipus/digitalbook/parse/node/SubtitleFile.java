package com.unipus.digitalbook.parse.node;

/**
 * 字幕文件对象
 */
public class SubtitleFile {
    private String fileName;
    private String fileUrl;
    private String fileType;
    private Long fileSize;
    private String suffix;
    private String subtitleTitle;

    // Getters and Setters
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }

    public String getFileUrl() { return fileUrl; }
    public void setFileUrl(String fileUrl) { this.fileUrl = fileUrl; }

    public String getFileType() { return fileType; }
    public void setFileType(String fileType) { this.fileType = fileType; }

    public Long getFileSize() { return fileSize; }
    public void setFileSize(Long fileSize) { this.fileSize = fileSize; }

    public String getSuffix() { return suffix; }
    public void setSuffix(String suffix) { this.suffix = suffix; }

    public String getSubtitleTitle() { return subtitleTitle; }
    public void setSubtitleTitle(String subtitleTitle) { this.subtitleTitle = subtitleTitle; }
}
