package com.unipus.digitalbook.parse.node;

import java.util.List;

/**
 * 视频节点对象
 */
public class VideoNode {
    private String fileUrl;
    private String fileName;
    private String coverImage;
    private String mediaName;
    private Long fileSize;
    private Long duration;
    private String width;
    private String height;
    private List<SubtitleFile> subtitleFiles;

    // Getters and Setters
    public String getFileUrl() { return fileUrl; }
    public void setFileUrl(String fileUrl) { this.fileUrl = fileUrl; }

    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }

    public String getCoverImage() { return coverImage; }
    public void setCoverImage(String coverImage) { this.coverImage = coverImage; }

    public String getMediaName() { return mediaName; }
    public void setMediaName(String mediaName) { this.mediaName = mediaName; }

    public Long getFileSize() { return fileSize; }
    public void setFileSize(Long fileSize) { this.fileSize = fileSize; }

    public Long getDuration() { return duration; }
    public void setDuration(Long duration) { this.duration = duration; }

    public String getWidth() { return width; }
    public void setWidth(String width) { this.width = width; }

    public String getHeight() { return height; }
    public void setHeight(String height) { this.height = height; }

    public List<SubtitleFile> getSubtitleFiles() { return subtitleFiles; }
    public void setSubtitleFiles(List<SubtitleFile> subtitleFiles) { this.subtitleFiles = subtitleFiles; }

    @Override
    public String toString() {
        return "VideoNode{" +
                "fileUrl='" + fileUrl + '\'' +
                ", fileName='" + fileName + '\'' +
                ", mediaName='" + mediaName + '\'' +
                ", fileSize=" + fileSize +
                ", duration=" + duration +
                '}';
    }
}
