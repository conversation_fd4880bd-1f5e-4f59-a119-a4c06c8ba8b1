package com.unipus.digitalbook.parse;

import com.unipus.digitalbook.parse.node.QuestionNode;
import com.unipus.digitalbook.parse.node.VideoNode;

import java.util.ArrayList;
import java.util.List;

/**
 * 解析后的章节内容对象
 */
public class ParsedChapterContent {
    private List<VideoNode> videoNodes = new ArrayList<>();
    private List<QuestionNode> questionNodes = new ArrayList<>();

    public void addVideoNode(VideoNode videoNode) {
        this.videoNodes.add(videoNode);
    }

    public void addQuestionNode(QuestionNode questionNode) {
        this.questionNodes.add(questionNode);
    }

    public List<VideoNode> getVideoNodes() {
        return videoNodes;
    }

    public List<QuestionNode> getQuestionNodes() {
        return questionNodes;
    }
}

