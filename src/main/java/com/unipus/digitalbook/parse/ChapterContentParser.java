package com.unipus.digitalbook.parse;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.parse.node.QuestionNode;
import com.unipus.digitalbook.parse.node.SubtitleFile;
import com.unipus.digitalbook.parse.node.VideoNode;

import java.util.ArrayList;
import java.util.List;

/**
 * 章节内容结构JSON解析器
 * 用于解析章节内容JSON，并提取视频URL和题目中的视频URL
 */
public class ChapterContentParser {

    /**
     * 解析章节内容JSON
     * @param jsonString 章节内容JSON字符串
     * @return ParsedChapterContent 解析后的章节内容对象
     */
    public static ParsedChapterContent parseChapterContent(String jsonString) {
        JSONObject rootObject = JSON.parseObject(jsonString);
        JSONObject root = rootObject.getJSONObject("root");

        ParsedChapterContent content = new ParsedChapterContent();

        if (root != null && root.containsKey("children")) {
            JSONArray children = root.getJSONArray("children");
            parseChildren(children, content);
        }

        return content;
    }

    /**
     * 递归解析children节点
     * @param children children JSONArray
     * @param content 解析结果容器
     */
    private static void parseChildren(JSONArray children, ParsedChapterContent content) {
        if (children == null) return;

        for (int i = 0; i < children.size(); i++) {
            JSONObject child = children.getJSONObject(i);
            if (child == null) continue;

            String type = child.getString("type");

            // 处理视频节点
            if ("insert-video".equals(type)) {
                VideoNode videoNode = parseVideoNode(child);
                if (videoNode != null) {
                    content.addVideoNode(videoNode);
                }
            }

            // 处理题目节点
            if ("question-block".equals(type)) {
                QuestionNode questionNode = parseQuestionNode(child);
                if (questionNode != null) {
                    content.addQuestionNode(questionNode);
                }
            }

            // 递归处理子节点
            if (child.containsKey("children")) {
                JSONArray subChildren = child.getJSONArray("children");
                parseChildren(subChildren, content);
            }
        }
    }

    /**
     * 解析视频节点
     * @param videoObject 视频节点JSON对象
     * @return VideoNode 视频节点对象
     */
    private static VideoNode parseVideoNode(JSONObject videoObject) {
        JSONObject mediaFile = videoObject.getJSONObject("mediaFile");
        if (mediaFile == null) return null;

        VideoNode videoNode = new VideoNode();
        videoNode.setFileUrl(mediaFile.getString("fileUrl"));
        videoNode.setFileName(mediaFile.getString("fileName"));
        videoNode.setCoverImage(mediaFile.getString("coverImage"));
        videoNode.setMediaName(mediaFile.getString("mediaName"));
        videoNode.setFileSize(mediaFile.getLong("fileSize"));
        videoNode.setDuration(mediaFile.getLong("duration"));
        videoNode.setWidth(videoObject.getString("width"));
        videoNode.setHeight(videoObject.getString("height"));

        // 解析字幕文件
        JSONArray subtitleFiles = mediaFile.getJSONArray("videoSubtitleFile");
        if (subtitleFiles != null) {
            List<SubtitleFile> subtitles = new ArrayList<>();
            for (int i = 0; i < subtitleFiles.size(); i++) {
                JSONObject subtitle = subtitleFiles.getJSONObject(i);
                SubtitleFile subtitleFile = new SubtitleFile();
                subtitleFile.setFileName(subtitle.getString("fileName"));
                subtitleFile.setFileUrl(subtitle.getString("fileUrl"));
                subtitleFile.setFileType(subtitle.getString("fileType"));
                subtitleFile.setFileSize(subtitle.getLong("fileSize"));
                subtitleFile.setSuffix(subtitle.getString("suffix"));
                subtitleFile.setSubtitleTitle(subtitle.getString("subtitleTitle"));
                subtitles.add(subtitleFile);
            }
            videoNode.setSubtitleFiles(subtitles);
        }

        return videoNode;
    }

    /**
     * 解析题目节点
     * @param questionObject 题目节点JSON对象
     * @return QuestionNode 题目节点对象
     */
    private static QuestionNode parseQuestionNode(JSONObject questionObject) {
        QuestionNode questionNode = new QuestionNode();
        questionNode.setQuestionType(questionObject.getString("questionType"));
        questionNode.setQuestionGroup(questionObject.getString("questionGroup"));

        String questionData = questionObject.getString("questionData");
        questionNode.setQuestionData(questionData);

        // 从questionData中提取视频URL
        if (questionData != null && !questionData.isEmpty()) {
            List<String> videoUrls = extractVideoUrlsFromQuestionData(questionData);
            questionNode.setVideoUrls(videoUrls);
        }

        return questionNode;
    }

    /**
     * 从questionData字符串中提取视频URL
     * @param questionData 题目数据字符串
     * @return List<String> 视频URL列表
     */
    private static List<String> extractVideoUrlsFromQuestionData(String questionData) {
        List<String> videoUrls = new ArrayList<>();

        try {
            // 尝试解析questionData为JSON
            JSONObject questionJson = JSON.parseObject(questionData);
            extractVideoUrlsFromJson(questionJson, videoUrls);
        } catch (Exception e) {
            // 如果不是JSON格式，使用正则表达式提取URL
            extractVideoUrlsWithRegex(questionData, videoUrls);
        }

        return videoUrls;
    }

    /**
     * 从JSON对象中递归提取视频URL
     * @param jsonObject JSON对象
     * @param videoUrls URL列表容器
     */
    private static void extractVideoUrlsFromJson(JSONObject jsonObject, List<String> videoUrls) {
        if (jsonObject == null) return;

        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);

            if (value instanceof String) {
                String strValue = (String) value;
                if (isVideoUrl(strValue)) {
                    videoUrls.add(strValue);
                }
            } else if (value instanceof JSONObject) {
                extractVideoUrlsFromJson((JSONObject) value, videoUrls);
            } else if (value instanceof JSONArray) {
                JSONArray array = (JSONArray) value;
                for (int i = 0; i < array.size(); i++) {
                    Object item = array.get(i);
                    if (item instanceof JSONObject) {
                        extractVideoUrlsFromJson((JSONObject) item, videoUrls);
                    } else if (item instanceof String && isVideoUrl((String) item)) {
                        videoUrls.add((String) item);
                    }
                }
            }
        }
    }

    /**
     * 使用正则表达式从字符串中提取视频URL
     * @param text 文本内容
     * @param videoUrls URL列表容器
     */
    private static void extractVideoUrlsWithRegex(String text, List<String> videoUrls) {
        // 匹配常见的视频URL模式
        String[] patterns = {
                "https?://[\\w\\-\\.]+\\.[a-zA-Z]{2,}/[\\w\\-\\./%\\?&=]*\\.mp4[\\w\\-\\./%\\?&=]*",
                "https?://[\\w\\-\\.]+\\.[a-zA-Z]{2,}/[\\w\\-\\./%\\?&=]*\\.avi[\\w\\-\\./%\\?&=]*",
                "https?://[\\w\\-\\.]+\\.[a-zA-Z]{2,}/[\\w\\-\\./%\\?&=]*\\.mov[\\w\\-\\./%\\?&=]*",
                "https?://[\\w\\-\\.]+\\.[a-zA-Z]{2,}/[\\w\\-\\./%\\?&=]*\\.wmv[\\w\\-\\./%\\?&=]*",
                "https?://[\\w\\-\\.]+\\.[a-zA-Z]{2,}/[\\w\\-\\./%\\?&=]*\\.flv[\\w\\-\\./%\\?&=]*",
                "https?://[\\w\\-\\.]+\\.[a-zA-Z]{2,}/[\\w\\-\\./%\\?&=]*\\.mkv[\\w\\-\\./%\\?&=]*"
        };

        for (String pattern : patterns) {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(text);
            while (m.find()) {
                String url = m.group();
                if (!videoUrls.contains(url)) {
                    videoUrls.add(url);
                }
            }
        }
    }

    /**
     * 判断字符串是否为视频URL
     * @param url 待判断的字符串
     * @return boolean 是否为视频URL
     */
    private static boolean isVideoUrl(String url) {
        if (url == null || url.isEmpty()) return false;

        String lowerUrl = url.toLowerCase();
        return lowerUrl.startsWith("http") &&
                (lowerUrl.contains(".mp4") ||
                        lowerUrl.contains(".avi") ||
                        lowerUrl.contains(".mov") ||
                        lowerUrl.contains(".wmv") ||
                        lowerUrl.contains(".flv") ||
                        lowerUrl.contains(".mkv"));
    }

    /**
     * 提取所有视频URL的便捷方法
     * @param jsonString 章节内容JSON字符串
     * @return List<String> 所有视频URL列表
     */
    public static List<String> extractAllVideoUrls(String jsonString) {
        ParsedChapterContent content = parseChapterContent(jsonString);
        List<String> allVideoUrls = new ArrayList<>();

        // 添加视频节点中的URL
        for (VideoNode videoNode : content.getVideoNodes()) {
            if (videoNode.getFileUrl() != null) {
                allVideoUrls.add(videoNode.getFileUrl());
            }
        }

        // 添加题目节点中的视频URL
        for (QuestionNode questionNode : content.getQuestionNodes()) {
            allVideoUrls.addAll(questionNode.getVideoUrls());
        }

        return allVideoUrls;
    }

    /**
     * 提取视频节点中的URL
     * @param jsonString 章节内容JSON字符串
     * @return List<String> 视频节点URL列表
     */
    public static List<String> extractVideoNodeUrls(String jsonString) {
        ParsedChapterContent content = parseChapterContent(jsonString);
        List<String> videoUrls = new ArrayList<>();

        for (VideoNode videoNode : content.getVideoNodes()) {
            if (videoNode.getFileUrl() != null) {
                videoUrls.add(videoNode.getFileUrl());
            }
        }

        return videoUrls;
    }

    /**
     * 提取题目节点中的视频URL
     * @param jsonString 章节内容JSON字符串
     * @return List<String> 题目中的视频URL列表
     */
    public static List<String> extractQuestionVideoUrls(String jsonString) {
        ParsedChapterContent content = parseChapterContent(jsonString);
        List<String> videoUrls = new ArrayList<>();

        for (QuestionNode questionNode : content.getQuestionNodes()) {
            videoUrls.addAll(questionNode.getVideoUrls());
        }

        return videoUrls;
    }




    public static void main(String[] args) {
        // 示例JSON字符串（这里使用您提供的JSON数据）
        String jsonString = "";

        try {
            // 1. 完整解析
            ParsedChapterContent content = ChapterContentParser.parseChapterContent(jsonString);
            System.out.println("视频节点数量: " + content.getVideoNodes().size());
            System.out.println("题目节点数量: " + content.getQuestionNodes().size());

            // 2. 提取所有视频URL
            List<String> allVideoUrls = ChapterContentParser.extractAllVideoUrls(jsonString);
            System.out.println("\n所有视频URL:");
            for (String url : allVideoUrls) {
                System.out.println("- " + url);
            }

            // 3. 只提取视频节点中的URL
            List<String> videoNodeUrls = ChapterContentParser.extractVideoNodeUrls(jsonString);
            System.out.println("\n视频节点URL:");
            for (String url : videoNodeUrls) {
                System.out.println("- " + url);
            }

            // 4. 只提取题目中的视频URL
            List<String> questionVideoUrls = ChapterContentParser.extractQuestionVideoUrls(jsonString);
            System.out.println("\n题目中的视频URL:");
            for (String url : questionVideoUrls) {
                System.out.println("- " + url);
            }

            // 5. 详细信息输出
            System.out.println("\n=== 详细信息 ===");
            for (VideoNode videoNode : content.getVideoNodes()) {
                System.out.println(videoNode);
            }

            for (QuestionNode questionNode : content.getQuestionNodes()) {
                System.out.println(questionNode);
            }

        } catch (Exception e) {
            System.err.println("解析错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
