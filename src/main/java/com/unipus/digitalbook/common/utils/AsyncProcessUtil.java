package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.common.exception.business.BizException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 通用异步处理工具类
 * 提供统一的同步/异步处理机制
 *
 * @param <T> 处理结果类型
 */
@Component
@Slf4j
public class AsyncProcessUtil<T> {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private VirtualThreadPoolManager virtualThreadPoolManager;

    private static final long REDIS_EXPIRE_SECONDS = 300L;
    private static final String REDIS_KEY_PREFIX = "ASYNC_PROCESS:";
    private static final String PROCESS_STATE_RUNNING = "running";
    private static final String PROCESS_STATE_FINISHED = "finished";
    private static final String PROCESS_STATE_ERROR = "error";

    /**
     * 执行处理任务，支持同步或异步模式
     * -
     * 该方法通过 identifier 参数生成唯一的任务标识，用于防止重复提交和记录任务执行状态。
     * 任务状态包括：运行中（running）、已完成（finished）、异常（error），存储于 Redis 中，
     * 键名为 ASYNC_PROCESS:identifier，过期时间为5分钟。
     * -
     * 执行逻辑：
     * 1. 若 async 为 true：任务将提交至虚拟线程池异步执行，立即返回 defaultResult
     * 2. 若 async 为 false：任务在当前线程同步执行，返回实际执行结果
     * -
     * @param async          是否异步执行：true 表示异步，false 表示同步
     * @param identifier     任务唯一标识符，建议使用业务ID或UUID
     * @param task           要执行的具体任务逻辑
     * @param defaultResult  异步模式下的默认返回值
     * @return 同步模式下返回任务执行结果；异步模式下返回 defaultResult
     * @throws BizException 如果任务已在执行中抛出异常（防止重复提交）
     */
    public T process(boolean async, String identifier, Supplier<T> task, T defaultResult) {
        if(isProcessRunning(identifier)){
            log.debug("任务处理中，请稍后再试: {}", identifier);
            throw new BizException("任务处理中，请稍后再试");
        }

        if (async) {
            // 异步执行 - 使用统一的虚拟线程池管理器
            virtualThreadPoolManager.executeAsync(() -> {
                try {
                    setProcessState(identifier, PROCESS_STATE_RUNNING);
                    task.get();
                    setProcessState(identifier, PROCESS_STATE_FINISHED);
                    log.debug("异步任务完成: {}", identifier);
                } catch (Exception e) {
                    setProcessState(identifier, PROCESS_STATE_ERROR);
                    log.error("异步任务执行异常: {}", identifier, e);
                    throw e;
                }
            });

            // 异步模式下返回默认值
            return defaultResult;
        } else {
            // 同步执行
            try {
                setProcessState(identifier, PROCESS_STATE_RUNNING);
                var resul = task.get();
                setProcessState(identifier, PROCESS_STATE_FINISHED);
                return resul;
            } catch (Exception e) {
                setProcessState(identifier, PROCESS_STATE_ERROR);
                log.error("同步任务执行异常: {}", identifier, e);
                throw e;
            }
        }
    }

    /**
     * 检查处理任务是否正在运行
     *
     * @param identifier 标识符
     * @return true表示正在运行，false表示未运行
     */
    public boolean isProcessRunning(String identifier) {
        return PROCESS_STATE_RUNNING.equals(getProcessState(identifier));
    }

    private String getProcessState(String identifier) {
        return stringRedisTemplate.opsForValue().get(getRedisKey(identifier));
    }

    private void setProcessState(String identifier, String state) {
        stringRedisTemplate.opsForValue().set(getRedisKey(identifier), state, REDIS_EXPIRE_SECONDS, TimeUnit.SECONDS);
    }

    private String getRedisKey(String identifier) {
        return REDIS_KEY_PREFIX + identifier;
    }
}