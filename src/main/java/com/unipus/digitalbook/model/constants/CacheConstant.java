package com.unipus.digitalbook.model.constants;

/**
 * <AUTHOR>
 * @date 2024/10/16 下午3:24
 */
public class CacheConstant {
    // 用户登录ServiceTicket缓存KEY前缀
    public static final String REDIS_AUTH_PREFIX="Auth:UserId-ST:";
    // 用户信息缓存KEY前缀
    public static final String REDIS_USR_PREFIX="UserInfo:Id-Info:";
    // 第三方用户信息缓存KEY前缀
    public static final String REDIS_THIRD_PARTY_USR_PREFIX="ThirdParty-UserInfo:Id-Info:";
    // 组织树信息缓存KEY
    public static final String REDIS_ORG_KEY="OrgInfo:Org-Tree";
    // 用户接口权限信息缓存KEY
    public static final String REDIS_API_PERMISSION_KEY="ApiPermissionRoles";
    // 数据权限信息缓存KEY前缀
    public static final String REDIS_DATA_PERMISSION_KEY="DataPermission:RID-";

    // 用户信息保持时间(默认1天)
    public static final Long REDIS_USER_TIMEOUT_SECONDS = 60 * 60 * 24L;

    // 默认数据保持时间(默认1周)
    public static final Long REDIS_TIMEOUT_SECONDS = 7*60*60*24L;

    // 用户试卷实例保持时间(默认1天)
    public static final Long REDIS_USER_PAPER_INSTANCE_TIMEOUT_HOURS = 24L;

    // 用户异步作答回调
    public static final String USER_ANSWER_CALLBACK_PREFIX = "UserAnswer:Callback:";
    // 用户异步作答结果
    public static final String USER_ANSWER_TEMP_STORAGE_PREFIX = "UserAnswer:TempStorage:";

    // 用户信息缓存KEY前缀
    public static final String REDIS_PLATFORM_USR_PREFIX="PlatformUserInfo:Id-Info:";

    // COS预签名URL缓存KEY前缀
    public static final String REDIS_COS_PRESIGNED_URL_PREFIX="COS:PresignedUrl:";

    // COS预签名URL缓存过期时间(默认50秒，比实际过期时间短10秒)
    public static final Long REDIS_COS_PRESIGNED_URL_TIMEOUT_SECONDS = 50L;

    // 章节内容缓存KEY前缀
    public static final String REDIS_COS_CHAPTER_CONTENT_PREFIX ="COS:ChapterContent:";

    // 章节内容缓存过期时间(默认30分钟)
    public static final Long REDIS_CHAPTER_CONTENT_TIMEOUT_SECONDS = 30 * 60L;

    // 快照内容缓存KEY前缀
    public static final String REDIS_COS_BOOK_TEMPORARY_SNAPSHOT_CONTENT_PREFIX = "COS:BookTemporarySnapshot:Content:";

    // 快照内容缓存过期时间(默认30分钟)
    public static final Long REDIS_COS_BOOK_TEMPORARY_SNAPSHOT_CONTENT_TIMEOUT_SECONDS = 30 * 60L;

    // COS转码模板缓存KEY前缀
    public static final String REDIS_COS_MEDIA_TRANSCODE_PREFIX = "COS:MediaTranscode:";

    // COS转码模板缓存过期时间(默认7天)
    public static final Long REDIS_COS_MEDIA_TEMPLATE_TIMEOUT_SECONDS = 7 * 24 * 60 * 60L;

    // COS转码模板BloomFilter缓存KEY前缀
    public static final String REDIS_COS_MEDIA_TRANSCODE_BLOOM_FILTER_PREFIX = "COS:MediaTranscode:BloomFilter:";

    // COS媒体信息缓存KEY前缀
    public static final String REDIS_COS_MEDIA_INFO_PREFIX = "COS:MediaInfo:";

    // 词汇学练服务token
    public static final String REDIS_WORD_PRACTICE_API_SERVICE_TOKEN_PREFIX = "WordPractice:ApiServiceToken:";




    // 阅读时长zset KEY键前缀
    public static final String ACTIVE_READING_SESSIONS_KEY = "active_reading_time_";
    // 阅读时长session Key 键前缀
    public static final String READING_TIME_SESSION_PREFIX = "reading_time:";
    // 定时任务计算未心跳的时长
    public static final long NO_HEARTBEAT_SECONDS = 61;
    // 计算结束阅读时长Redis锁
    public static final String END_READING_LOCK = "{end_reading_lock}";

    private CacheConstant() {
        super();
    }
}
