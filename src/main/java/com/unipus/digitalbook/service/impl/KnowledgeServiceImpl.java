package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.knowledge.KnowledgeCreateException;
import com.unipus.digitalbook.common.exception.knowledge.KnowledgeNotExistException;
import com.unipus.digitalbook.dao.BookKnowledgeInfoMapper;
import com.unipus.digitalbook.model.dto.DataListDTO;
import com.unipus.digitalbook.model.dto.knowledge.BookKnowledgeInfoGetDTO;
import com.unipus.digitalbook.model.enums.EnableEnum;
import com.unipus.digitalbook.model.enums.KnowledgeStatusEnum;
import com.unipus.digitalbook.model.params.knowledge.KnowledgeAddParam;
import com.unipus.digitalbook.model.po.knowledge.BookKnowledgeInfoPO;
import com.unipus.digitalbook.service.KnowledgeService;
import com.unipus.digitalbook.service.remote.restful.knowledge.KnowledgeApiService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.Knowledge;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgePrimaryIdRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeTag;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.*;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.BaseKnowledgeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeResourceNodeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeVersionListResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.ResourceLabelResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 图谱创建相关接口
 */
@Service
@Slf4j
public class KnowledgeServiceImpl implements KnowledgeService {

    @Resource
    KnowledgeApiService knowledgeApiService;

    @Resource
    BookKnowledgeInfoMapper bookKnowledgeInfoMapper;

    @Override
    public BookKnowledgeInfoGetDTO courseKnowledgeInfoGet(String bookId, Integer publishedStatus) {
        BookKnowledgeInfoGetDTO infoGetDTO = new BookKnowledgeInfoGetDTO();
        BookKnowledgeInfoPO bookKnowledgeInfoPO = bookKnowledgeInfoMapper.selectByBookId(bookId);
        if (null == bookKnowledgeInfoPO) {
            return infoGetDTO;
        }

        //有图谱的情况，设置图谱基本信息
        String knowledgeId = bookKnowledgeInfoPO.getKnowledgeId();
        BaseKnowledgeResponse<Knowledge> remoteRes = knowledgeApiService.knowledgeGet(knowledgeId);
        Knowledge knowledge = remoteRes.getResult();
        knowledge.setCourseKnowledgeId(bookKnowledgeInfoPO.getId());

        infoGetDTO.setHasKnowledge(Boolean.TRUE);
        infoGetDTO.setKnowledge(knowledge);
        return infoGetDTO;
    }

    @Override
    public BookKnowledgeInfoPO knowledgeAdd(KnowledgeAddParam params, Long opUserId) {
        //如果数据已经存在，则直接返回
        BookKnowledgeInfoPO bookKnowledgeInfoPO = bookKnowledgeInfoMapper.selectByBookId(params.getBooId());
        if (null != bookKnowledgeInfoPO) {
            log.warn("数据已存在，不可多次添加");
            return bookKnowledgeInfoPO;
        }

        KnowledgeAddRequest knowledgeAddRequest = params.toKnowledgeAddRequest();
        BaseKnowledgeResponse<String> result = knowledgeApiService.knowledgeAdd(knowledgeAddRequest);
        //三方接口返回的知识图谱Id
        String knowledgeId = result.getResult();
        if (StringUtils.isBlank(knowledgeId)) {
            throw new KnowledgeCreateException();
        }

        BookKnowledgeInfoPO insertKnowledgeInfo = new BookKnowledgeInfoPO();
        insertKnowledgeInfo.setBookId(params.getBooId());
        insertKnowledgeInfo.setName(params.getName());
        insertKnowledgeInfo.setDescription(params.getBackground());
        insertKnowledgeInfo.setKnowledgeId(knowledgeId);
        insertKnowledgeInfo.setCreateBy(opUserId);
        insertKnowledgeInfo.setUpdateBy(opUserId);
        insertKnowledgeInfo.setCreateTime(new Date());
        insertKnowledgeInfo.setUpdateTime(new Date());
        bookKnowledgeInfoMapper.insertSelective(insertKnowledgeInfo);
        return insertKnowledgeInfo;
    }

    @Override
    public void knowledgeUpdate(KnowledgeUpdateRequest params, Long opUserId) {
        knowledgeApiService.knowledgeUpdate(params);

        BookKnowledgeInfoPO updateKnowledgeInfo = new BookKnowledgeInfoPO();
        updateKnowledgeInfo.setId(params.getId());
        updateKnowledgeInfo.setName(params.getName());
        updateKnowledgeInfo.setDescription(params.getDescription());
        updateKnowledgeInfo.setKnowledgeId(params.getKnowledgeId());
        updateKnowledgeInfo.setUpdateBy(opUserId);
        bookKnowledgeInfoMapper.updateByPrimaryKeySelective(updateKnowledgeInfo);
    }

    @Override
    public void knowledgeDelete(KnowledgePrimaryIdRequest params, Long opUserId) {
        knowledgeApiService.knowledgeDelete(params.getKnowledgeId());
        BookKnowledgeInfoPO updateInfo = new BookKnowledgeInfoPO();
        updateInfo.setEnable(EnableEnum.DISABLE.getCode());
        updateInfo.setUpdateTime(new Date());
        updateInfo.setUpdateBy(opUserId);
        bookKnowledgeInfoMapper.updateByPrimaryKeySelective(updateInfo);
    }

    @Override
    public Knowledge knowledgeGet(String knowledgeId) {
        BookKnowledgeInfoPO knowledgeGetInfo = new BookKnowledgeInfoPO();
        knowledgeGetInfo.setKnowledgeId(knowledgeId);
        BookKnowledgeInfoPO bookKnowledgeInfoPO = bookKnowledgeInfoMapper.selectBySelective(knowledgeGetInfo);
        if (null == bookKnowledgeInfoPO) {
            throw new KnowledgeNotExistException();
        }

        BaseKnowledgeResponse<Knowledge> remoteRes = knowledgeApiService.knowledgeGet(knowledgeId);
        if (null == remoteRes || null == remoteRes.getResult()) {
            log.warn("ipub库存在知识图谱节点，但三方图谱数据已找不到，可能删除！");
            throw new KnowledgeNotExistException();
        }

        Knowledge remoteResResult = remoteRes.getResult();
        remoteResResult.setCourseKnowledgeId(bookKnowledgeInfoPO.getId());
        return remoteResResult;
    }

    @Override
    public void knowledgePublish(KnowledgePrimaryIdRequest params, Long opUserId) {
        KnowledgePublishRequest request = new KnowledgePublishRequest();
        request.setKnowledgeId(params.getKnowledgeId());
        request.setDescription(params.getDescription());
        knowledgeApiService.knowledgePublish(request);

        BookKnowledgeInfoPO updateInfo = new BookKnowledgeInfoPO();
        updateInfo.setId(params.getId());
        updateInfo.setUpdateTime(new Date());
        updateInfo.setUpdateBy(opUserId);
        updateInfo.setDescription(KnowledgeStatusEnum.PUBLISHED.getDesc());
        bookKnowledgeInfoMapper.updateByPrimaryKeySelective(updateInfo);
    }

    @Override
    public void knowledgeCheck(String knowledgeId) {
        KnowledgeCheckRequest request = new KnowledgeCheckRequest();
        request.setKnowledgeId(knowledgeId);
        knowledgeApiService.knowledgeCheck(request);
    }

    @Override
    public List<KnowledgeVersionListResponse> knowledgeVersionList(String  knowledgeId) {
        BaseKnowledgeResponse<List<KnowledgeVersionListResponse>> response = knowledgeApiService.knowledgeVersionList(knowledgeId);
        return CollectionUtils.isEmpty(response.getResult()) ? new ArrayList<>() : response.getResult();
    }

    @Override
    public String knowledgeFileUpload(MultipartFile file) {
        BaseKnowledgeResponse<String> remoteRes = knowledgeApiService.knowledgeFileUpload(file);
        return remoteRes.getResult();
    }

    @Override
    public DataListDTO<Knowledge> knowledgeList(String keyword) {
        BaseKnowledgeResponse<List<Knowledge>> remoteRes = knowledgeApiService.knowledgeList(keyword);
        return new DataListDTO<>(remoteRes.getResult());
    }

    @Override
    public DataListDTO<Knowledge> knowledgeGraphSearch(String keyword) {
        BaseKnowledgeResponse<List<Knowledge>> remoteRes = knowledgeApiService.knowledgeGraphList(keyword);
        return new DataListDTO<>(remoteRes.getResult());
    }

    @Override
    public List<KnowledgeResourceNodeResponse.ResourceNode> getKnowledgeResourceNodeDetail(String knowledgeId) {
        List<KnowledgeResourceNodeResponse.ResourceNode>  result = new ArrayList<>();

        BaseKnowledgeResponse<List<KnowledgeResourceNodeResponse>> remoteRes = knowledgeApiService.getKnowledgeResourceNodeDetail(knowledgeId);
        if (null == remoteRes || CollectionUtils.isEmpty(remoteRes.getResult())) {
            return result;
        }

        List<KnowledgeResourceNodeResponse> remoteResResult = remoteRes.getResult();
        for (KnowledgeResourceNodeResponse knowledgeResourceNodeResponse : remoteResResult) {
            if (CollectionUtils.isEmpty(knowledgeResourceNodeResponse.getNodes())) {
                log.warn("此图谱暂时没有任何知识点信息，图谱Id：{}", knowledgeId);
                continue;
            }
            KnowledgeResourceNodeResponse.ResourceNode dto = new KnowledgeResourceNodeResponse.ResourceNode();
            dto.setId(knowledgeResourceNodeResponse.getGraphId());
            dto.setName(knowledgeResourceNodeResponse.getName());
            dto.setChildren(knowledgeResourceNodeResponse.getNodes());
            result.add(dto);
        }
        return result;
    }

    @Override
    public List<KnowledgeTag> getResourceLabelDetail() {
        List<KnowledgeTag> knowledgeTags = new ArrayList<>();

        BaseKnowledgeResponse<List<ResourceLabelResponse>> remoteRes = knowledgeApiService.getResourceLabelDetail();
        List<ResourceLabelResponse> resourcelabelList = remoteRes.getResult();
        if (CollectionUtils.isEmpty(resourcelabelList)) {
            return knowledgeTags;
        }
        for (ResourceLabelResponse labelResponse : resourcelabelList) {
            KnowledgeTag knowledgeTag = new KnowledgeTag();
            knowledgeTag.setId(labelResponse.getId());
            knowledgeTag.setContent(labelResponse.getType());
            knowledgeTag.setChildren(labelResponse.getLabelSet());
            knowledgeTags.add(knowledgeTag);
        }

        return knowledgeTags;
    }
}
