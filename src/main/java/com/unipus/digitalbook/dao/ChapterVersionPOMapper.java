package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.po.chapter.ChapterVersionPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【chapter_version(章节版本表)】的数据库操作Mapper
 * @createDate 2024-12-16 22:49:08
 * @Entity com.unipus.digitalbook.model.po.chapter.ChapterVersionPO
 */
public interface ChapterVersionPOMapper {


    /**
     * 查询所有章节版本信息
     *
     * @return 返回所有章节版本信息的列表
     */
    List<ChapterVersionPO> selectAll();

    /**
     * 删除 chapter_version记录
     *
     * @param id java.lang.Long
     * @return int
     * <AUTHOR>
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新建 chapter_version记录
     *
     * @param row com.unipus.digitalbook.model.po.chapter.ChapterVersionPO
     * @return int
     * <AUTHOR>
     */
    int insertSelective(ChapterVersionPO row);

    /**
     * 查询chapter_version记录
     *
     * @param id java.lang.Long
     * @return ChapterVersionPO
     * <AUTHOR>
     */
    ChapterVersionPO selectByPrimaryKey(Long id);

    /**
     * 更新 chapter_version记录
     *
     * @param row com.unipus.digitalbook.model.po.chapter.ChapterVersionPO
     * @return int
     * <AUTHOR>
     */
    int updateByPrimaryKeySelective(ChapterVersionPO row);

    /**
     * 更新 chapter_version记录
     *
     * @param row com.unipus.digitalbook.model.po.chapter.ChapterVersionPO
     * @return int
     * <AUTHOR>
     */
    int updateByPrimaryKeyWithBLOBs(ChapterVersionPO row);

    /**
     * 更新 chapter_version记录
     *
     * @param row com.unipus.digitalbook.model.po.chapter.ChapterVersionPO
     * @return int
     * <AUTHOR>
     */
    int updateByPrimaryKey(ChapterVersionPO row);

    ChapterVersionPO selectLatestVersionByChapterId(@Param("chapterId") String chapterId);

    /**
     * 根据id列表,查询章节版本列表
     *
     * @param idList id列表
     * @return 章节版本列表
     */
    List<ChapterVersionPO> selectVersionListByIdList(@Param("idList") List<Long> idList);

    /**
     * 根据章节id和版本号,查询章节版本
     *
     * @param chapterId     章节id
     * @param versionNumber 版本号
     * @return 章节版本
     */
    ChapterVersionPO selectByChapterIdAndVersionNumber(@Param("chapterId") String chapterId, @Param("versionNumber") String versionNumber);


    List<ChapterVersionPO> selectByChapterId(@Param("chapterId") String chapterId);

    /**
     * 根据章节id,查询最新版本的章节版本
     *
     * @param chapterIds 章节id
     * @return 章节版本
     */
    List<ChapterVersionPO> selectLatestVersionByChapterIds(@Param("chapterIds") List<String> chapterIds);

    /**
     * 根据章节id,查询最新版本的章节版本id
     *
     * @param chapterIds 章节id
     * @return 章节版本id
     */
    List<Long> selectLatestVersionIdByChapterIds(@Param("chapterIds") List<String> chapterIds);

    /**
     * 根据章节id和版本号,查询章节版本id
     *
     * @param chapterId     章节id
     * @param versionNumber 版本号
     * @return 章节版本id
     */
    Long selectIdByChapterIdAndVersionNumber(@Param("chapterId") String chapterId, @Param("versionNumber") String versionNumber);

    /**
     * 根据章节id,查询最新版本的章节版本id
     *
     * @param chapterId 章节id
     * @return 章节版本id
     */
    Long selectLatestVersionIdByChapterId(@Param("chapterId") String chapterId);


    List<ChapterVersionPO> selectCatalogByIds(@Param("ids") List<Long> ids);


    List<ChapterVersionPO> selectResourceByIds(@Param("ids") List<Long> ids);

    /**
     * 根据章节ID分页查询章节版本列表
     *
     * @param chapterId  章节ID
     * @param pageParams 分页参数
     * @return 章节版本列表
     */
    List<ChapterVersionPO> selectPageByChapterId(@Param("chapterId") String chapterId, @Param("page") PageParams pageParams);

    /**
     * 根据章节ID统计章节版本数量
     *
     * @param chapterId 章节ID
     * @return 章节版本数量
     */
    Integer countByChapterId(@Param("chapterId") String chapterId);

    /**
     * 根据bookVersionId和chapterId查询章节版本
     *
     * @param bookVersionId bookVersionId
     * @param chapterId     chapterId
     * @return 章节版本关系信息
     */
    ChapterVersionPO selectByBookVersionIdAndChapterId(@Param("bookVersionId") Long bookVersionId, @Param("chapterId") String chapterId);

    /**
     * 根据章节版本id列表,查询章节id列表
     *
     * @param chapterVersionIds 章节版本id列表
     * @return 章节id列表
     */
    List<ChapterVersionPO> selectChapterIdsByIds(@Param("ids") List<Long> chapterVersionIds);

    List<ChapterVersionPO> selectChapterContentByChapterId(@Param("chapterId") String chapterId);

    int updateChapterContentById(ChapterVersionPO row);

    /**
     * 根据章节版本id,查询章节节点列表
     *
     * @param chapterVersionId 章节版本id
     * @return 章节节点列表
     */
    ChapterVersionPO selectChapterNodeByChapterVersionId(Long chapterVersionId);

    /**
     * 根据章节id和版本号,查询章节版本id
     *
     * @param chapterId     章节id
     * @param versionNumber 版本号
     * @return 章节版本id
     */
    Long selectVersionIdByChapterIdAndVersionNumber(String chapterId, String versionNumber);


    /**
     * 根据章节id列表,查询最新**保存**版本的章节版本,不包括历史版本
     * 优化版
     *
     * @param chapterIds 章节id列表
     * @return 章节版本列表
     */
    List<ChapterVersionPO> selectLatestVersionByChapterIdsOpt(@Param("chapterIds") List<String> chapterIds);
}